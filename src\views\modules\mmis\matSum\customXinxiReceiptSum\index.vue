<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :query-form="queryForm"
    :queryMethod="queryXinxiStorageDetailList"
    show-export
    :export-config="exportConfig"
    name="信息科入库明细"
    :single-line="false"
    :bordered="false"
    :show-operation-button="false"
    @query-complete="updateTotalStats"
    :paging="false"
    class="receipt-detail-table"
    striped
    :row-props="rowProps"
  >
    <template #extendRightHeader>
      <n-card size="small" class="summary-card">
        <n-grid :cols="4" :x-gap="12">
          <!-- 第一行标签 -->
          <n-grid-item>
            <n-text>入库单数</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>入库物资数量</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>入库金额汇总</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>物资大类统计</n-text>
          </n-grid-item>

          <!-- 第二行数值 -->
          <n-grid-item>
            <n-number-animation :from="0" :to="totalDocs" />
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="totalCount" />
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="totalAmount" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <div class="category-stats">
              <div>低值易耗品: {{ categoryStats.lowValue }}</div>
              <div>耗材: {{ categoryStats.consumables }}</div>
              <div>配件: {{ categoryStats.parts }}</div>
            </div>
          </n-grid-item>
        </n-grid>
      </n-card>
    </template>

    <template #extendFormItems>
      <n-form-item label="入库日期" path="dateRange">
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          clearable
          :shortcuts="dateShortcuts"
          @update:value="handleDateRangeChange"
        />
      </n-form-item>
      <n-form-item label="仓库名称">
        <n-select
          v-model:value="queryForm.wrhsAddr"
          :options="xinxiWrhsInfo"
          placeholder="请选择信息科仓库"
          filterable
          clearable
          multiple
          :max-tag-count="4"
        />
      </n-form-item>
      <n-form-item label="手工单号">
        <n-input v-model:value="queryForm.manualDocNum" :clearable="true" />
      </n-form-item>
      <n-form-item label="物资名称">
        <n-input v-model:value="queryForm.name" :clearable="true" placeholder="支持模糊查询" />
      </n-form-item>
      <n-form-item label="物资代码">
        <n-input v-model:value="queryForm.itemNum" :clearable="true" />
      </n-form-item>
      <n-form-item label="物资大类">
        <n-select
          v-model:value="queryForm.itemTypeCode"
          :options="materialTypeOptions"
          placeholder="请选择物资大类"
          clearable
        />
      </n-form-item>
    </template>

    <template #extendButtons>
      <n-button type="primary" @click="handleQuery">查询</n-button>
      <n-button @click="handleReset">重置</n-button>
    </template>

    <template #contentTop>
      <div class="summary">
        <div class="title">信息科入库明细 - 日期: {{ formatDate(queryForm.startTime) }} 到: {{ formatDate(queryForm.endTime) }}</div>
        <div class="summary-info">
          <span>入库金额: ¥{{ totalAmount.toFixed(2) }}</span>
          <span>入库数量: {{ totalCount }}</span>
          <span>低值易耗品: {{ categoryStats.lowValue }}项</span>
          <span>耗材: {{ categoryStats.consumables }}项</span>
          <span>配件: {{ categoryStats.parts }}项</span>
        </div>
      </div>
    </template>
  </j-crud>
</template>

<script setup lang="ts">
  import { h, ref, computed, onMounted, watch } from 'vue'
  import { queryXinxiStorageDetailList } from '@/api/mmis/matReceipt/AsetStorageWeb'
  import { CRUDColumnInterface } from '@/types/comps/crud'
  import JCrud from '@/components/common/crud/index.vue'
  import {
    NButton,
    NFormItem,
    NDatePicker,
    NInput,
    NCard,
    NGrid,
    NGridItem,
    NText,
    NNumberAnimation,
    useMessage,
    NSelect,
  } from 'naive-ui'
  import dayjs from 'dayjs'
  import JPGlobal from '@/types/common/jutil'
  import { queryAllMmisWrhsInfo } from '@/api/mmis/wrhsInfo/wrhsInfoWeb'
  import { ExcelType } from '@/utils/excel'

  const message = window.$message
  const crudRef = ref()

  // 统计数据
  const totalDocs = ref(0)
  const totalCount = ref(0)
  const totalAmount = ref(0)
  const categoryStats = ref({
    lowValue: 0,    // 0401 低值易耗品
    consumables: 0, // 0402 耗材
    parts: 0        // 0403 配件
  })

  // 仓库信息
  const allWrhsInfo = ref<any[]>([])
  const xinxiWrhsInfo = computed(() => {
    // 过滤出信息科相关的仓库
    return allWrhsInfo.value.filter(item =>
      item.label?.includes('信息科') ||
      item.value?.includes('xinxi') ||
      item.value?.includes('XINXI')
    )
  })

  // 物资大类选项
  const materialTypeOptions = ref([
    { label: '低值易耗品', value: '0401' },
    { label: '耗材', value: '0402' },
    { label: '配件', value: '0403' }
  ])

  // 日期快捷选项
  const dateShortcuts = {
    今天: () => {
      const today = dayjs().valueOf()
      return [today, today] as [number, number]
    },
    昨天: () => {
      const yesterday = dayjs().subtract(1, 'day').valueOf()
      return [yesterday, yesterday] as [number, number]
    },
    本周: () => {
      const start = dayjs().startOf('week').valueOf()
      const end = dayjs().endOf('week').valueOf()
      return [start, end] as [number, number]
    },
    上周: () => {
      const start = dayjs().subtract(1, 'week').startOf('week').valueOf()
      const end = dayjs().subtract(1, 'week').endOf('week').valueOf()
      return [start, end] as [number, number]
    },
    本月: () => {
      const start = dayjs().startOf('month').valueOf()
      const end = dayjs().endOf('month').valueOf()
      return [start, end] as [number, number]
    },
    上月: () => {
      const start = dayjs().subtract(1, 'month').startOf('month').valueOf()
      const end = dayjs().subtract(1, 'month').endOf('month').valueOf()
      return [start, end] as [number, number]
    },
    本季度: () => {
      const start = dayjs().startOf('quarter').valueOf()
      const end = dayjs().endOf('quarter').valueOf()
      return [start, end] as [number, number]
    },
    上季度: () => {
      const start = dayjs().subtract(1, 'quarter').startOf('quarter').valueOf()
      const end = dayjs().subtract(1, 'quarter').endOf('quarter').valueOf()
      return [start, end] as [number, number]
    },
    本年: () => {
      const start = dayjs().startOf('year').valueOf()
      const end = dayjs().endOf('year').valueOf()
      return [start, end] as [number, number]
    },
    上一年: () => {
      const start = dayjs().subtract(1, 'year').startOf('year').valueOf()
      const end = dayjs().subtract(1, 'year').endOf('year').valueOf()
      return [start, end] as [number, number]
    },
  }

  // 查询表单
  const queryForm = ref({
    hospitalId: 'zjxrmyy', // 医院ID
    billDate: '', // 单日查询
    startTime: dayjs().startOf('month').format('YYYY-MM-DD'), // 起始日期
    endTime: dayjs().endOf('month').format('YYYY-MM-DD'), // 结束日期
    manualDocNum: '', // 手工单号
    itemNum: '', // 物资编码
    name: '', // 物资名称
    wrhsAddr: [] as string[], // 仓库代码
    itemTypeCode: '', // 物资大类代码
  })

  // 日期范围
  const dateRange = ref<[number, number] | null>([
    dayjs().startOf('month').valueOf(),
    dayjs().endOf('month').valueOf(),
  ])

  // 导出配置
  const exportConfig = ref<ExcelType>({
    excelName: '信息科入库明细',
    excelType: '.xlsx',
    sheetName: '信息科入库明细',
    enableSummary: true,
    borderStyle: {
      enable: true,
      borderType: 'thin',
    },
    writeFootnoteConfig: {
      enable: true,
      footnote: '数据来源：中江县人民医院智慧财务系统 - 信息科入库明细',
      exportUser: true,
      exportDate: true,
    },
  })

  // 行属性配置
  const rowProps = (_row: any) => {
    return {
      style: {
        cursor: 'pointer',
      },
    }
  }

  // 日期格式化
  const formatDate = (date: string) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : ''
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value: [number, number] | null) => {
    if (value) {
      queryForm.value.startTime = dayjs(value[0]).format('YYYY-MM-DD')
      queryForm.value.endTime = dayjs(value[1]).format('YYYY-MM-DD')
    } else {
      queryForm.value.startTime = ''
      queryForm.value.endTime = ''
    }
  }

  // 查询
  const handleQuery = () => {
    crudRef.value?.query()
  }

  // 重置
  const handleReset = () => {
    queryForm.value = {
      hospitalId: 'zjxrmyy',
      billDate: '',
      startTime: dayjs().startOf('month').format('YYYY-MM-DD'),
      endTime: dayjs().endOf('month').format('YYYY-MM-DD'),
      manualDocNum: '',
      itemNum: '',
      name: '',
      wrhsAddr: [],
      itemTypeCode: '',
    }
    dateRange.value = [
      dayjs().startOf('month').valueOf(),
      dayjs().endOf('month').valueOf(),
    ]
    crudRef.value?.query()
  }

  // 更新统计数据
  const updateTotalStats = (data: any[]) => {
    if (!data || !Array.isArray(data)) {
      totalDocs.value = 0
      totalCount.value = 0
      totalAmount.value = 0
      categoryStats.value = { lowValue: 0, consumables: 0, parts: 0 }
      return
    }

    // 统计入库单数（去重）
    const uniqueDocs = new Set(data.map(item => item.docmentNum))
    totalDocs.value = uniqueDocs.size

    // 统计总数量和总金额
    totalCount.value = data.reduce((sum, item) => sum + (Number(item.num) || 0), 0)
    totalAmount.value = data.reduce((sum, item) => sum + (Number(item.amt) || 0), 0)

    // 统计物资大类
    const stats = { lowValue: 0, consumables: 0, parts: 0 }
    data.forEach(item => {
      switch (item.itemTypeCode) {
        case '0401':
          stats.lowValue++
          break
        case '0402':
          stats.consumables++
          break
        case '0403':
          stats.parts++
          break
      }
    })
    categoryStats.value = stats
  }

  // 渲染单据号
  const renderDocNum = (row: any) => {
    return h(
      'span',
      {
        style: {
          color: '#1890ff',
          cursor: 'pointer',
        },
        onClick: () => {
          // 可以添加单据详情查看功能
          message.info(`查看单据: ${row.docmentNum}`)
        },
      },
      row.docmentNum
    )
  }

  // 渲染手工单号
  const renderManualDocNum = (row: any) => {
    return row.manualDocNum || '-'
  }

  // 格式化行号
  const formatLineNum = (row: any) => {
    return row.lineNum || '-'
  }

  // 渲染物资大类
  const renderItemType = (row: any) => {
    const typeMap: Record<string, { text: string; color: string }> = {
      '0401': { text: '低值易耗品', color: '#52c41a' },
      '0402': { text: '耗材', color: '#1890ff' },
      '0403': { text: '配件', color: '#fa8c16' },
    }

    const type = typeMap[row.itemTypeCode]
    if (type) {
      return h(
        'span',
        {
          style: {
            color: type.color,
            fontWeight: 'bold',
          },
        },
        type.text
      )
    }
    return row.itemTypeName || '-'
  }

  // 表格列配置
  const columns = ref<Array<CRUDColumnInterface>>([
    {
      title: '单据日期',
      key: 'billDate',
      width: 100,
      form: {
        type: 'date',
        span: 6,
      },
    },
    {
      title: '单据号',
      key: 'docmentNum',
      width: 140,
      align: 'center',
      render: renderDocNum,
    },
    {
      title: '手工单号',
      key: 'manualDocNum',
      width: 140,
      render: renderManualDocNum,
      form: {
        type: 'input',
        span: 6,
      },
    },
    {
      title: '行号',
      key: 'lineNum',
      width: 70,
      render: formatLineNum,
    },
    {
      title: '物资大类',
      key: 'itemTypeGroup',
      align: 'center',
      children: [
        {
          title: '物资大类代码',
          key: 'itemTypeCode',
          width: 100,
        },
        {
          title: '物资大类名称',
          key: 'itemTypeName',
          width: 120,
          render: renderItemType,
        },
      ],
    },
    {
      title: '物资代码',
      key: 'itemNum',
      width: 100,
      form: {
        type: 'input',
        span: 6,
      },
    },
    {
      title: '物资名称',
      key: 'name',
      width: 140,
      form: {
        type: 'input',
        span: 6,
        placeholder: '支持模糊查询',
      },
    },
    {
      title: '规格型号',
      key: 'modspec',
      width: 100,
    },
    {
      title: '批号',
      key: 'auditBchno',
      width: 80,
    },
    {
      title: '库位代码',
      key: 'wrhsAddr',
      width: 100,
    },
    {
      title: '数量',
      key: 'num',
      width: 80,
      summary: true,
    },
    {
      title: '单价',
      key: 'price',
      width: 80,
      render: (row: any) => {
        return row.price || 0
      },
    },
    {
      title: '金额',
      key: 'amt',
      width: 100,
      summary: true,
      render: (row: any) => {
        return row.amt || 0
      },
    },
    {
      title: '入库部门名称',
      key: 'inOrgName',
      width: 120,
    },
    {
      title: '业务员',
      key: 'inEmp',
      width: 80,
    },
    {
      title: '记账人',
      key: 'appyer',
      width: 80,
    },
    {
      title: '审核人',
      key: 'auditor',
      width: 80,
    },
    {
      title: '备注',
      key: 'remark',
      width: 120,
    },
  ])

  // 获取仓库信息
  const getWrhsInfo = async () => {
    try {
      const res = await queryAllMmisWrhsInfo({})
      if (res && res.data) {
        allWrhsInfo.value = res.data.map((item: any) => ({
          label: item.wrhsName,
          value: item.wrhsCode,
        }))
      }
    } catch (error) {
      console.error('获取仓库信息失败:', error)
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    getWrhsInfo()
  })

  // 监听日期范围变化
  watch(dateRange, (newVal) => {
    if (newVal) {
      handleDateRangeChange(newVal)
    }
  })
</script>

<style scoped>
.receipt-detail-table {
  margin-top: 16px;
}

.summary-card {
  margin-bottom: 16px;
}

.summary {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
  margin-bottom: 16px;
}

.summary .title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.summary-info {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.summary-info span {
  color: #666;
  font-size: 14px;
}

.category-stats {
  font-size: 12px;
  line-height: 1.4;
}

.category-stats div {
  margin-bottom: 2px;
}
</style>