package com.jp.med.mmis.modules.matReceipt.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.mmis.modules.common.dto.MmisAuditRcdfmDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 物资入库
 *
 * <AUTHOR>
 * @email -
 * @date 2024-02-19 15:01:07
 */
@Data
@TableName("mmis_aset_storage")
public class MmisAsetStorageDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 进货单号
     */
    @TableField("po_num")
    private String poNum;

    /**
     * 采购验收单号
     */
    @TableField("receipt_num")
    private String receiptNum;

    /**
     * 开单日期
     */
    @TableField("bill_date")
    private String billDate;

    /**
     * 供应商
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Integer supplierId;
    /**
     * 单据号
     */
    @TableField("docment_num")
    private String docmentNum;

    /**
     * 手工单号
     */
    @TableField("manual_doc_num")
    private String manualDocNum;

    /**
     * 运单号
     */
    @TableField("track_num")
    private String trackNum;

    /**
     * 发票单号
     */
    @TableField("invoice_num")
    private String invoiceNum;

    /**
     * $column.comments
     */
    @TableField("remark")
    private String remark;

    /**
     * 业务部门（申请人科室id）
     */
    @TableField("appy_org_id")
    private String appyOrgId;

    /**
     * 业务员（ 申请人的员工编号）
     */
    @TableField("appyer")
    private String appyer;

    /**
     * 仓库代码
     */
    @TableField("wrhs_code")
    private String wrhsCode;

    /**
     * 业务类别(入出库类型code)
     */
    @TableField("type_code")
    private String typeCode;

    /**
     * $column.comments
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * $column.comments
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 审核批次号
     */
    @TableField("audit_bchno")
    private String auditBchno;

    /**
     * 审核状态
     */
    @TableField("chk_state")
    private String chkState;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 发票记录表ID（多个）
     */
    @TableField("invo_id")
    private String invoId;

    /**
     * 发票附件（多个）
     */
    @TableField("att")
    private String att;

    /**
     * 发票附件（多个）
     */
    @TableField("att_name")
    private String attName;

    /**
     * 审核流程详情
     */
    @TableField(exist = false)
    private List<MmisAuditRcdfmDto> mmisAuditDetails;

    /**
     * 入库申请明细
     */
    @TableField(exist = false)
    private List<MmisAsetStorageDetailDto> mmisStockDetailDtos;

    /**
     * 审核页面
     */
    @TableField(exist = false)
    private String audit;

    /**
     * 摘要搜索关键词（用于模糊搜索物资名称）
     */
    @TableField(exist = false)
    private String invtSummaryKeyword;

    /**
     * 物资选择
     */
    @TableField(exist = false)
    private Map<Integer, Map<String, Integer>> materialMap;

    /**
     * 文件对应发票id的map
     */
    @TableField(exist = false)
    private Map<String, Long> fileIdentifierMap;

    /**
     * 附件
     */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    /**
     * 审核流程详情
     */
    @TableField(exist = false)
    private List<AuditDetail> auditDetails;

    /**
     * 入库状态
     */
    @TableField("in_status")
    private String inStatus;

    /**
     * 入库人
     */
    @TableField("in_emp")
    private String inEmp;

    /**
     * 入库执行人部门
     */
    @TableField("in_org_id")
    private String inOrgId;

    /**
     * 入库时间
     */
    @TableField("in_time")
    private String inTime;

    /**
     * 入库备注
     */
    @TableField("in_remark")
    private String inRemark;

    /**
     * 费用报销状态： 0处理中，1已报销，2 报销取消
     */
    @TableField("reim_status_flag")
    private String reimStatusFlag;

    /**
     * 结账期号
     */
    @TableField("settle_period_num")
    private String settlePeriodNum;

    /**
     * 品名《搜索用的
     */
    @TableField(exist = false)
    private String name;

    /**
     * 根据仓库搜索用的 - 支持单值查询和列表查询
     */
    @TableField(exist = false)
    private Object wrhsAddr; // 使用Object类型以支持String和List<String>

    /**
     * 多仓库查询列表 - 支持多个仓库同时查询
     */
    @TableField(exist = false)
    private List<String> warehouseCodes;

    /**
     * 获取wrhsAddr
     * 
     * @return 如果是String直接返回，如果是List返回第一个元素
     */
    public String getWrhsAddr() {
        if (wrhsAddr == null) {
            return null;
        }
        if (wrhsAddr instanceof String) {
            return (String) wrhsAddr;
        }
        if (wrhsAddr instanceof List && !((List<?>) wrhsAddr).isEmpty()) {
            Object firstItem = ((List<?>) wrhsAddr).get(0);
            return firstItem != null ? firstItem.toString() : null;
        }
        return wrhsAddr.toString();
    }

    /**
     * 设置wrhsAddr
     * 
     * @param wrhsAddr 可以是String或List<String>
     */
    public void setWrhsAddr(Object wrhsAddr) {
        this.wrhsAddr = wrhsAddr;
    }

    @TableField(exist = false)
    private String startTime;

    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String orgID;

    @TableField(exist = false)
    private String itemNum;

    // 打印入库单用的字段 接收用的
    @TableField(exist = false)
    private String invType;

    /**
     * 物资大类代码（用于信息科入库明细筛选）
     * 支持的值：
     * - 0401: 低值易耗品（信息科库房）
     * - 0402: 耗材（信息科库房）
     * - 0403: 配件（信息科库房）
     */
    @TableField(exist = false)
    private String itemTypeCode;

    /**
     * 用于查账时查入库单的详情的字段
     */
    @TableField(exist = false)
    private String matUniqueCode;

    @TableField(exist = false)
    private String appyTime;

    /**
     * 用于查找详情的申请id
     * applyId
     */
    @TableField(exist = false)
    private Integer applyId;

    /**
     * 入库单ID列表
     */
    @TableField(exist = false)
    private List<Integer> storageIdList;

    /**
     * 前端传的项目名称，如果不传就自动生成
     */
    @TableField(exist = false)
    private String itemName;

    // 以下字段用于物资报销反写状态的字段
    /**
     * 报销Id
     */
    @TableField(exist = false)
    private Integer reimId;

    /**
     * 报销详情id集合（采购详情id）
     */
    @TableField(exist = false)
    private List<Integer> purcDetailIds;

    /**
     * 下载发票文件用于触发上传发票
     */
    @TableField(exist = false)
    private String filePath;

    /**
     * 是否查询已结账的物资，随便传个值就行
     * 传的有值表示查询已结账的入出库单（有结账期号的入出库单）
     */
    @TableField(exist = false)
    private String querySettle;

    /**
     * 是否按照供应商名称进行排序
     * 1:按供应商名称排序 为空不排序
     */
    @TableField(exist = false)
    private String orderBySN;

    @TableField(exist = false)
    private List<Integer> detailIdList;

    // 用户有权限的仓库代码列表
    @TableField(exist = false)
    private List<String> wrhsNeedShow;

    /**
     * 处理人员工编号（用于权限控制查询）
     */
    @TableField(exist = false)
    private String processEmpCode;

    /** 开始日期（用于时间段查询） */
    @TableField(exist = false)
    private String startDate;

    /** 结束日期（用于时间段查询） */
    @TableField(exist = false)
    private String endDate;

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getProcessEmpCode() {
        return processEmpCode;
    }

    public void setProcessEmpCode(String processEmpCode) {
        this.processEmpCode = processEmpCode;
    }

}
