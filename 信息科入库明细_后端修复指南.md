# 信息科入库明细功能 - 后端修复指南 🔧

## 问题描述

前端调用 `queryXinxiStorageDetailList` API 时出现以下错误：

```
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'itemTypeCode' in 'class com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto'
```

## 根本原因

后端 SQL 查询中使用了 `itemTypeCode` 参数进行物资大类筛选，但 `MmisAsetStorageDto` 类中缺少对应的字段定义。

## 修复方案

### 步骤 1：修改 MmisAsetStorageDto.java

**文件位置：** `med-mmis/src/main/java/com/jp/med/mmis/modules/matReceipt/dto/MmisAsetStorageDto.java`

**修改内容：** 在类中添加以下字段（建议在其他 `@TableField(exist = false)` 字段附近）：

```java
/**
 * 物资大类代码（用于信息科入库明细筛选）
 * 支持的值：
 * - 0401: 低值易耗品（信息科库房）
 * - 0402: 耗材（信息科库房）  
 * - 0403: 配件（信息科库房）
 */
@TableField(exist = false)
private String itemTypeCode;
```

### 步骤 2：添加 Getter/Setter 方法

如果类没有使用 `@Data` 注解，需要手动添加：

```java
public String getItemTypeCode() {
    return itemTypeCode;
}

public void setItemTypeCode(String itemTypeCode) {
    this.itemTypeCode = itemTypeCode;
}
```

**注意：** 如果类已经使用了 `@Data` 注解（Lombok），则会自动生成 getter/setter 方法，无需手动添加。

### 步骤 3：重新编译和部署

1. 保存文件
2. 重新编译后端项目
3. 重启后端服务

## 验证修复

### 1. 检查服务启动

确保后端服务正常启动，没有编译错误。

### 2. 测试前端功能

1. 访问信息科入库明细页面：`/modules/mmis/matSum/customXinxiReceiptSum`
2. 尝试进行查询操作
3. 测试物资大类筛选功能

### 3. 验证 API 调用

使用浏览器开发者工具或 Postman 测试：

```
POST /mmis/mmisAsetStorage/queryXinxiStorageDetailList
Content-Type: application/json

{
  "hospitalId": "zjxrmyy",
  "startTime": "2024-01-01",
  "endTime": "2024-12-31",
  "itemTypeCode": "0401"
}
```

## 相关文件说明

### SQL 查询文件
- **位置：** `med-mmis/src/main/resources/mapper/matReceipt/read/MmisAsetStorageReadMapper.xml`
- **方法：** `queryXinxiStorageDetailList`
- **关键 SQL 片段：**
```xml
<if test="itemTypeCode != null and itemTypeCode != ''">
    AND SUBSTRING(d.item_num, 1, 4) = #{itemTypeCode,jdbcType=VARCHAR}
</if>
```

### 前端 API 调用
- **位置：** `src/api/mmis/matReceipt/AsetStorageWeb.ts`
- **方法：** `queryXinxiStorageDetailList`

### 前端界面组件
- **位置：** `src/views/modules/mmis/matSum/customXinxiReceiptSum/index.vue`

## 物资大类映射

| 代码 | 名称 | 描述 |
|------|------|------|
| 0401 | 低值易耗品 | 信息科库房 |
| 0402 | 耗材 | 信息科库房 |
| 0403 | 配件 | 信息科库房 |

## 故障排除

### 如果修复后仍有问题：

1. **检查编译：** 确保项目重新编译成功
2. **检查缓存：** 清理 IDE 缓存和项目缓存
3. **检查导入：** 确保 `@TableField` 注解正确导入
4. **检查语法：** 确保 Java 语法正确，没有拼写错误
5. **查看日志：** 检查后端启动日志是否有错误信息

### 常见错误：

- **编译错误：** 检查 Java 语法和导入语句
- **注解错误：** 确保使用正确的 MyBatis-Plus 注解
- **字段名错误：** 确保字段名与 SQL 中使用的参数名一致

## 完成标志

修复成功的标志：
- ✅ 后端服务正常启动
- ✅ 前端页面可以正常加载
- ✅ 查询功能正常工作
- ✅ 物资大类筛选功能正常
- ✅ 不再出现 "There is no getter for property named 'itemTypeCode'" 错误

---

**修复完成后，信息科入库明细功能将完全可用！** 🎉
