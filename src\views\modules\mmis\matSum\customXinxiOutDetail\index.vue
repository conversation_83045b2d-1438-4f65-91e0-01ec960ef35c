<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :query-form="queryForm"
    :queryMethod="queryOutboundDetailList"
    show-export
    :export-config="exportConfig"
    name="信息科出库明细汇总"
    :single-line="false"
    :bordered="true"
    :show-operation-button="false"
    @query-complete="handleQueryComplete"
    :paging="false"
    striped
    :row-props="rowProps"
    height="800px"
  >
    <template #extendRightHeader>
      <n-card size="small" class="summary-card">
        <n-grid :cols="4" :x-gap="12">
          <!-- 第一行标签 -->
          <n-grid-item>
            <n-text>出库单数</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>出库物资数量</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>出库金额汇总</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>物资大类分布</n-text>
          </n-grid-item>

          <!-- 第二行数值 -->
          <n-grid-item>
            <div v-if="isLoading">
              <n-spin size="small" />
            </div>
            <n-number-animation v-else-if="hasQueried" :from="0" :to="totalDocs" />
            <n-text v-else depth="3">--</n-text>
          </n-grid-item>
          <n-grid-item>
            <div v-if="isLoading">
              <n-spin size="small" />
            </div>
            <n-number-animation v-else-if="hasQueried" :from="0" :to="totalCount" />
            <n-text v-else depth="3">--</n-text>
          </n-grid-item>
          <n-grid-item>
            <div v-if="isLoading">
              <n-spin size="small" />
            </div>
            <n-number-animation v-else-if="hasQueried" :from="0" :to="totalAmount" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
            <n-text v-else depth="3">--</n-text>
          </n-grid-item>
          <n-grid-item>
            <div v-if="isLoading">
              <n-spin size="small" />
            </div>
            <div v-else-if="hasQueried" class="category-stats">
              <n-tag size="small" type="success">低值易耗品: {{ categoryStats.lowValue }}</n-tag>
              <n-tag size="small" type="info">耗材: {{ categoryStats.consumables }}</n-tag>
              <n-tag size="small" type="warning">配件: {{ categoryStats.parts }}</n-tag>
            </div>
            <n-text v-else depth="3">--</n-text>
          </n-grid-item>
        </n-grid>
      </n-card>
    </template>

    <template #extendFormItems>
      <n-form-item label="出库日期" path="dateRange">
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          clearable
          :shortcuts="dateShortcuts"
          @update:value="handleDateRangeChange"
        />
      </n-form-item>
      <n-form-item label="物资大类">
        <n-select
          v-model:value="queryForm.itemTypeCode"
          :options="materialTypeOptions"
          placeholder="请选择物资大类"
          clearable
        />
      </n-form-item>
      <n-form-item label="信息科仓库">
        <n-select
          v-model:value="queryForm.wrhsAddr"
          :options="xinxiWrhsInfo"
          placeholder="请选择信息科仓库"
          filterable
          clearable
          multiple
          :max-tag-count="3"
        />
      </n-form-item>
      <n-form-item label="单据号">
        <n-input v-model:value="queryForm.manualDocNum" :clearable="true" />
      </n-form-item>
      <n-form-item label="物资名称">
        <n-input v-model:value="queryForm.name" :clearable="true" placeholder="支持模糊查询" />
      </n-form-item>
      <n-form-item label="物资代码">
        <n-input v-model:value="queryForm.itemNum" :clearable="true" />
      </n-form-item>
      <n-form-item label="申请科室">
        <n-select
          v-model:value="queryForm.appyOrgId"
          :options="allDeptInfo"
          placeholder="请选择申请科室"
          filterable
          clearable
        />
      </n-form-item>
      <n-form-item label="出库目标科室">
        <n-select
          v-model:value="queryForm.outTargetOrgId"
          :options="allDeptInfo"
          placeholder="请选择出库目标科室"
          filterable
          clearable
        />
      </n-form-item>
    </template>

    <template #extendButtons>
      <n-button type="primary" @click="handleQuery">查询</n-button>
      <n-button @click="handleReset">重置</n-button>
    </template>

    <template #contentTop>
      <div class="summary">
        <div class="title">📊 信息科出库明细汇总 - 日期: {{ formatDate(queryForm.startTime) }} 到: {{ formatDate(queryForm.endTime) }}</div>
        <div class="summary-info">
          <span v-if="hasQueried">💰 出库金额: {{ totalAmount.toFixed(2) }}</span>
          <span v-else>💰 出库金额: --</span>
          <span v-if="hasQueried">📦 出库合计: {{ totalCount }}</span>
          <span v-else>📦 出库合计: --</span>
          <span v-if="hasQueried">📋 单据数量: {{ totalDocs }}</span>
          <span v-else>📋 单据数量: --</span>
          <span v-if="queryForm.itemTypeCode">🏷️ 筛选类别: {{ getMaterialTypeName(queryForm.itemTypeCode) }}</span>
        </div>
      </div>
    </template>
  </j-crud>
</template>

<script setup lang="ts">
  import { h, ref, computed, onMounted, watch } from 'vue'
  import { queryOutboundDetailList } from '@/api/mmis/outBound/OutboundApplyWeb'
  import { CRUDColumnInterface } from '@/types/comps/crud'
  import JCrud from '@/components/common/crud/index.vue'
  import {
    NButton,
    NFormItem,
    NDatePicker,
    NInput,
    NCard,
    NGrid,
    NGridItem,
    NText,
    NNumberAnimation,
    useMessage,
    NSelect,
    NSpin,
    NTag,
  } from 'naive-ui'
  import dayjs from 'dayjs'
  import JPGlobal from '@/types/common/jutil'
  import { queryAllMmisWrhsInfo } from '@/api/mmis/wrhsInfo/wrhsInfoWeb'
  import { queryOrg } from '@/api/hrm/hrmOrg'
  import { ExcelType } from '@/utils/excel'

  const message = window.$message
  const crudRef = ref()

  // 导出配置
  const exportConfig = ref<ExcelType>({
    // 支持二级表头
    isComplex: true,
    // 开启汇总行
    enableSummary: false,
    // 导出前处理数据，添加汇总行
    exportBeforeProcess: (datas: any) => {
      if (!Array.isArray(datas)) {
        return []
      }

      // 复制数据，避免修改原数据
      const exportData = [...datas]

      // 计算汇总数据
      let totalAmt = 0
      let totalNum = 0

      // 预处理数据，确保数值类型正确
      exportData.forEach(item => {
        // 确保price是数字类型
        if (item.price !== undefined && item.price !== null) {
          // 如果是字符串，尝试转换为数字
          if (typeof item.price === 'string') {
            item.price = parseFloat(item.price) || 0
          }
        } else {
          item.price = 0
        }

        // 确保amt是数字类型
        if (item.amt !== undefined && item.amt !== null) {
          // 如果是字符串，尝试转换为数字
          if (typeof item.amt === 'string') {
            item.amt = parseFloat(item.amt) || 0
          }
        } else {
          item.amt = 0
        }

        // 确保num是数字类型
        if (item.num !== undefined && item.num !== null) {
          // 如果是字符串，尝试转换为数字
          if (typeof item.num === 'string') {
            item.num = parseFloat(item.num) || 0
          }
        } else {
          item.num = 0
        }

        totalAmt += item.amt || 0
        totalNum += item.num || 0
      })

      // 创建汇总行
      const summaryRow = {
        billDate: '汇总',
        docmentNum: '',
        manualDocNum: '',
        lineNum: '',
        itemTypeCode: '',
        itemTypeName: '',
        itemNum: '',
        name: '',
        modspec: '',
        auditBchno: '',
        wrhsAddr: '',
        num: totalNum,
        price: '',
        amt: totalAmt,
        appyOrgName: '',
        appyer: '',
        outEmp: '',
        outTagetOrgName: '',
        outAppyerName: '',
        remark: '',
        invTypeName: '',
        purpose: '',
      }

      // 添加汇总行到数据末尾
      exportData.push(summaryRow)

      return exportData
    },
    // 文件名包含日期
    excelName: `信息科出库明细汇总_${dayjs().format('YYYY-MM-DD')}`,
    // 工作表名称
    sheetName: '信息科出库明细汇总',
  })

  // 所有仓库信息
  const allWrhsInfo = ref<Array<any>>([])
  // 所有部门信息
  const allDeptInfo = ref<Array<any>>([])

  // 信息科仓库信息
  const xinxiWrhsInfo = computed(() => {
    // 过滤出信息科相关的仓库
    return allWrhsInfo.value.filter(item =>
      item.label?.includes('信息科') ||
      item.label?.includes('信息') ||
      item.value?.includes('xinxi') ||
      item.value?.includes('XINXI') ||
      item.value?.includes('info')
    )
  })

  // 物资大类选项
  const materialTypeOptions = ref([
    { label: '低值易耗品', value: '0401' },
    { label: '耗材', value: '0402' },
    { label: '配件', value: '0403' }
  ])

  // 获取物资大类名称
  const getMaterialTypeName = (code: string) => {
    const option = materialTypeOptions.value.find(item => item.value === code)
    return option ? option.label : code
  }

  // 查询所有仓库
  const queryAllWrhsInfoList = () => {
    queryAllMmisWrhsInfo({ useStatus: '1' }).then(res => {
      allWrhsInfo.value = []
      res.data.forEach((item: any) => {
        allWrhsInfo.value.push({
          label: item.wrhsName,
          value: item.wrhsCode,
        })
      })
    })
  }

  // 查询所有部门
  const queryAllDeptList = () => {
    queryOrg({ hospitalId: 'zjxrmyy' }).then(res => {
      allDeptInfo.value = []
      res.data.forEach((item: any) => {
        allDeptInfo.value.push({
          label: item.orgName,
          value: item.orgId,
        })
      })
    })
  }

  // 用于标注不同单据号的颜色
  const docColors = [
    '#e6f7ff', // 浅蓝
    '#f6ffed', // 浅绿
    '#fff7e6', // 浅橙
    '#fcf4ff', // 浅紫
    '#fff1f0', // 浅红
    '#f2f2f2', // 浅灰
  ]

  // 记录单据号与颜色的映射
  const docColorMap = ref<Record<string, string>>({})

  // 日期范围
  const dateRange = ref<[number, number]>([dayjs().startOf('month').valueOf(), dayjs().endOf('month').valueOf()])

  // 日期快捷选项
  const dateShortcuts = {
    本周: () => {
      const start = dayjs().startOf('week').valueOf()
      const end = dayjs().endOf('week').valueOf()
      return [start, end] as [number, number]
    },
    上周: () => {
      const start = dayjs().subtract(1, 'week').startOf('week').valueOf()
      const end = dayjs().subtract(1, 'week').endOf('week').valueOf()
      return [start, end] as [number, number]
    },
    本月: () => {
      const start = dayjs().startOf('month').valueOf()
      const end = dayjs().endOf('month').valueOf()
      return [start, end] as [number, number]
    },
    上个月: () => {
      const start = dayjs().subtract(1, 'month').startOf('month').valueOf()
      const end = dayjs().subtract(1, 'month').endOf('month').valueOf()
      return [start, end] as [number, number]
    },
    本年: () => {
      const start = dayjs().startOf('year').valueOf()
      const end = dayjs().endOf('year').valueOf()
      return [start, end] as [number, number]
    },
    上一年: () => {
      const start = dayjs().subtract(1, 'year').startOf('year').valueOf()
      const end = dayjs().subtract(1, 'year').endOf('year').valueOf()
      return [start, end] as [number, number]
    },
  }

  // 查询表单
  const queryForm = ref({
    hospitalId: 'zjxrmyy', // 医院ID
    billDate: '', // 单日查询
    startTime: dayjs().startOf('month').format('YYYY-MM-DD'), // 起始日期
    endTime: dayjs().endOf('month').format('YYYY-MM-DD'), // 结束日期
    manualDocNum: '', // 手工单号
    itemNum: '', // 物资编码
    name: '', // 物资名称
    wrhsAddr: [] as string[], // 仓库代码 - 修改为数组类型
    appyOrgId: '', // 申请科室ID
    outTargetOrgId: '', // 出库目标科室ID
    invType: '', // 出库类型
    purpose: '', // 用途代码
    itemTypeCode: '', // 🔥 新增：物资大类代码
  })

  // 表格数据
  const tableData = ref<any[]>([])

  // 统计数据
  const totalAmount = ref(0)
  const totalCount = ref(0)
  const totalDocs = ref(0)
  const isLoading = ref(false)  // 添加加载状态
  const hasQueried = ref(false) // 添加是否已查询标识

  // 物资大类统计
  const categoryStats = ref({
    lowValue: 0,    // 0401 低值易耗品
    consumables: 0, // 0402 耗材
    parts: 0        // 0403 配件
  })

  // 格式化行号，确保显示整数
  const formatLineNum = row => {
    const lineNum = row.lineNum || '-'
    const docNum = row.docmentNum || ''

    // 如果没有单据号，直接返回行号
    if (!docNum) return lineNum

    // 使用相同的颜色映射
    if (!docColorMap.value[docNum]) {
      const colorIndex = Object.keys(docColorMap.value).length % docColors.length
      docColorMap.value[docNum] = docColors[colorIndex]
    }

    return h(
      'div',
      {
        style: {
          backgroundColor: docColorMap.value[docNum],
          height: '70px',
          width: '110%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center', // 居中对齐
          padding: '0 8px',
          margin: '-12px -8px',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      },
      lineNum
    )
  }

  // 单据号渲染函数，为相同单据号添加相同背景色
  const renderDocNum = row => {
    const docNum = row.docmentNum || ''
    if (!docNum) return docNum

    // 为单据号分配颜色
    if (!docColorMap.value[docNum]) {
      const colorIndex = Object.keys(docColorMap.value).length % docColors.length
      docColorMap.value[docNum] = docColors[colorIndex]
    }

    return h(
      'div',
      {
        style: {
          backgroundColor: docColorMap.value[docNum],
          height: '70px',
          width: '110%',
          display: 'flex',
          alignItems: 'center',
          padding: '0 8px',
          margin: '-12px -8px',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      },
      docNum
    )
  }

  // 手工单号渲染函数
  const renderManualDocNum = row => {
    const manualDocNum = row.manualDocNum || ''
    const docNum = row.docmentNum || ''

    // 如果没有单据号，直接返回手工单号
    if (!docNum) return manualDocNum

    // 使用相同的颜色映射
    if (!docColorMap.value[docNum]) {
      const colorIndex = Object.keys(docColorMap.value).length % docColors.length
      docColorMap.value[docNum] = docColors[colorIndex]
    }

    return h(
      'div',
      {
        style: {
          backgroundColor: docColorMap.value[docNum],
          height: '70px',
          width: '110%',
          display: 'flex',
          alignItems: 'center',
          padding: '0 8px',
          margin: '-12px -8px',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      },
      manualDocNum
    )
  }

  // 渲染物资大类
  const renderItemType = (row: any) => {
    const typeMap: Record<string, { text: string; color: string }> = {
      '0401': { text: '低值易耗品', color: '#52c41a' },
      '0402': { text: '耗材', color: '#1890ff' },
      '0403': { text: '配件', color: '#fa8c16' },
    }

    const type = typeMap[row.itemTypeCode]
    if (type) {
      return h(
        'span',
        {
          style: {
            color: type.color,
            fontWeight: 'bold',
          },
        },
        type.text
      )
    }
    return row.itemTypeName || '-'
  }

  // 表格列配置
  const columns = ref<Array<CRUDColumnInterface>>([
    {
      title: '单据日期',
      key: 'billDate',
      width: 100,
      form: {
        type: 'date',
        span: 6,
      },
    },
    {
      title: '单据号',
      key: 'docmentNum',
      width: 140,
      align: 'center',
      render: renderDocNum,
    },
    {
      title: '手工单号',
      key: 'manualDocNum',
      width: 140,
      render: renderManualDocNum,
      form: {
        type: 'input',
        span: 6,
      },
    },
    {
      title: '行号',
      key: 'lineNum',
      width: 70,
      render: formatLineNum,
    },
    {
      title: '物资大类',
      key: 'itemTypeGroup',
      align: 'center',
      children: [
        {
          title: '物资大类代码',
          key: 'itemTypeCode',
          width: 100,
        },
        {
          title: '物资大类名称',
          key: 'itemTypeName',
          width: 120,
          render: renderItemType,
        },
      ],
    },
    {
      title: '物资代码',
      key: 'itemNum',
      width: 100,
      form: {
        type: 'input',
        span: 6,
      },
    },
    {
      title: '物资名称',
      key: 'name',
      width: 140,
      form: {
        type: 'input',
        span: 6,
        placeholder: '支持模糊查询',
      },
    },
    {
      title: '规格型号',
      key: 'modspec',
      width: 100,
    },
    {
      title: '批号',
      key: 'auditBchno',
      width: 80,
    },
    {
      title: '库位代码',
      key: 'wrhsAddr',
      width: 100,
    },
    {
      title: '数量',
      key: 'num',
      width: 80,
      summary: true,
    },
    {
      title: '单价',
      key: 'price',
      width: 80,
      render: row => {
        // 不使用toFixed，返回原始值让Excel自己处理格式化
        return row.price || 0
      },
    },
    {
      title: '金额',
      key: 'amt',
      width: 100,
      summary: true,
      render: row => {
        // 不使用toFixed，返回原始值让Excel自己处理格式化
        return row.amt || 0
      },
    },
    {
      title: '申请科室',
      key: 'appyOrgName',
      width: 120,
    },
    {
      title: '申请人',
      key: 'appyer',
      width: 80,
    },
    {
      title: '出库人',
      key: 'outEmp',
      width: 80,
    },
    {
      title: '出库目标科室',
      key: 'outTagetOrgName',
      width: 120,
    },
    {
      title: '出库申请人',
      key: 'outAppyer',
      width: 80,
    },
    {
      title: '备注',
      key: 'remark',
      width: 100,
    },
    {
      title: '出库类型',
      key: 'invTypeName',
      width: 120,
    },
    {
      title: '用途',
      key: 'purpose',
      width: 80,
    },
  ])

  // 更新总计数据
  const updateTotalStats = (res: any) => {
    console.log('API返回数据:', res)

    // 获取数据，处理可能的不同数据结构
    let dataList = []
    if (Array.isArray(res)) {
      dataList = res
    } else if (res?.records && Array.isArray(res.records)) {
      // 处理分页数据结构中的records数组
      dataList = res.records
    } else if (res?.list && Array.isArray(res.list)) {
      dataList = res.list
    } else if (res?.data?.list && Array.isArray(res.data.list)) {
      dataList = res.data.list
    } else if (res?.data?.records && Array.isArray(res.data.records)) {
      dataList = res.data.records
    } else if (crudRef.value?.originData && Array.isArray(crudRef.value.originData)) {
      dataList = crudRef.value.originData
    } else {
      console.error('无法获取有效的数据列表:', res)
      dataList = []
    }

    console.log('处理后的数据列表:', dataList.length)

    // 更新表格数据
    tableData.value = dataList || []
    totalAmount.value = 0
    totalCount.value = 0

    // 重置颜色映射
    docColorMap.value = {}

    // 重置物资大类统计
    categoryStats.value = {
      lowValue: 0,
      consumables: 0,
      parts: 0
    }

    // 使用Set收集唯一单据号
    const uniqueDocs = new Set()

    // 处理行号 - 根据相同单据号进行编号
    const docGroups = {}
    dataList.forEach((item: any) => {
      // 收集统计数据
      totalAmount.value += Number(item.amt) || 0
      totalCount.value += Number(item.num) || 0
      if (item.manualDocNum) {
        uniqueDocs.add(item.manualDocNum)
      }

      // 统计物资大类
      const itemTypeCode = item.itemTypeCode
      if (itemTypeCode === '0401') {
        categoryStats.value.lowValue += Number(item.num) || 0
      } else if (itemTypeCode === '0402') {
        categoryStats.value.consumables += Number(item.num) || 0
      } else if (itemTypeCode === '0403') {
        categoryStats.value.parts += Number(item.num) || 0
      }

      // 按单据号分组
      const docKey = item.docmentNum || ''
      if (!docGroups[docKey]) {
        docGroups[docKey] = []
      }
      docGroups[docKey].push(item)
    })

    // 重新分配行号
    Object.keys(docGroups).forEach(docNum => {
      docGroups[docNum].forEach((item, index) => {
        item.lineNum = index + 1
      })
    })

    totalDocs.value = uniqueDocs.size
    hasQueried.value = true  // 标记已查询
    isLoading.value = false  // 查询完成
  }

  // 创建汇总行
  const createSummary = (data: any) => {
    let res: any = {}
    let ct = 0
    for (let column of columns.value) {
      if (ct == 0) {
        res[column.key] = {
          value: '汇总',
        }
      } else {
        let sum = 0
        if (column.summary) {
          data.forEach((d: any) => (sum += d[column.key] || 0))
          res[column.key] = {
            value: h('p', { innerHTML: formatSum(sum) }),
          }
        } else {
          res[column.key] = {
            value: '',
          }
        }
      }
      ct++
    }
    return res
  }

  // 格式化合计金额
  const formatSum = (value: number) => {
    if (!value && value !== 0) return '0'

    // 如果数值大于1亿
    if (Math.abs(value / 100000000) > 1) {
      return (value / 100000000).toFixed(2) + '<span style="font-size: 10px">/亿</span>'
    }
    // 如果数值大于1万
    else if (Math.abs(value / 10000) > 1) {
      return (value / 10000).toFixed(2) + '<span style="font-size: 10px">/万</span>'
    }

    return value.toFixed(2)
  }

  // 监听数据变化，更新统计
  watch(
    () => crudRef.value?.originData,
    newVal => {
      if (newVal) {
        updateTotalStats(newVal)
      }
    },
    { deep: true }
  )

  // 日期范围变更处理
  const handleDateRangeChange = (val: [number, number] | null) => {
    if (val) {
      queryForm.value.startTime = dayjs(val[0]).format('YYYY-MM-DD')
      queryForm.value.endTime = dayjs(val[1]).format('YYYY-MM-DD')
      queryForm.value.billDate = '' // 清空单日查询
    } else {
      queryForm.value.startTime = ''
      queryForm.value.endTime = ''
    }
  }

  // 格式化日期
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '--'
    return dateStr
  }

  // 查询按钮点击
  const handleQuery = () => {
    isLoading.value = true  // 开始查询
    crudRef.value?.reload()
  }

  // 重置按钮点击
  const handleReset = () => {
    // 重置日期范围为当月
    dateRange.value = [dayjs().startOf('month').valueOf(), dayjs().endOf('month').valueOf()]

    // 重置查询表单
    Object.assign(queryForm.value, {
      hospitalId: 'zjxrmyy',
      billDate: '',
      startTime: dayjs().startOf('month').format('YYYY-MM-DD'),
      endTime: dayjs().endOf('month').format('YYYY-MM-DD'),
      manualDocNum: '',
      itemNum: '',
      name: '',
      wrhsAddr: [],
      appyOrgId: '',
      outTargetOrgId: '',
      invType: '',
      purpose: '',
      itemTypeCode: '', // 重置物资大类
      pageNum: 1,
      pageSize: 1000,
    })

    // 重新查询
    handleQuery()
  }

  // 页面加载时查询
  onMounted(() => {
    // 获取仓库列表
    queryAllWrhsInfoList()
    // 获取部门列表
    queryAllDeptList()

    // 不再自动查询，让用户主动点击查询
    // handleQuery()
  })

  // 行属性配置
  const rowProps = (row: any) => {
    return {
      style: {
        cursor: 'pointer',
      },
    }
  }

  // 查询完成处理
  const handleQueryComplete = (res: any) => {
    updateTotalStats(res)
  }
</script>

<style scoped>
  .outbound-detail-sum {
    padding: 16px;
    background-color: #fff;
  }

  .summary {
    margin-bottom: 16px;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #2d3436;
  }

  .summary-info {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
  }

  .summary-info span {
    font-size: 14px;
    color: #2d3436;
    padding: 4px 8px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .summary-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .category-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  :deep(.outbound-detail-table) {
    /* 表格样式 */
    .n-data-table {
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #ebeef5;
    }

    /* 表头样式 */
    .n-data-table-thead {
      background-color: #f8f9fa;
    }

    .n-data-table-th {
      font-weight: 600;
      color: #2d3436;
      padding: 12px 8px;
      border-bottom: 1px solid #ebeef5;
    }

    /* 单元格样式 */
    .n-data-table-td {
      padding: 12px 8px;
      border-bottom: 1px solid #ebeef5;
    }

    /* 斑马纹 */
    .n-data-table-tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    /* 鼠标悬浮效果 */
    .n-data-table-tr:hover {
      background-color: #e3f2fd !important;
      transition: background-color 0.3s ease;
    }

    /* 数字列右对齐 */
    .n-data-table-td[data-col-key='num'],
    .n-data-table-td[data-col-key='price'],
    .n-data-table-td[data-col-key='amt'] {
      text-align: right;
    }
  }

  /* 查询表单样式 */
  :deep(.n-form-item) {
    margin-right: 16px;
  }

  :deep(.n-button) {
    border-radius: 4px;
    padding: 8px 16px;
  }
</style>