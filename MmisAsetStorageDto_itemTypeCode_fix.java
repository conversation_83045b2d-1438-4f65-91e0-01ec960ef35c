/**
 * 🔧 MmisAsetStorageDto 字段修复方案
 * 
 * 问题：前端调用 queryXinxiStorageDetailList 时出现错误：
 * "There is no getter for property named 'itemTypeCode' in 'class com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto'"
 * 
 * 解决方案：在 MmisAsetStorageDto 类中添加 itemTypeCode 字段
 * 
 * 文件位置：med-mmis/src/main/java/com/jp/med/mmis/modules/matReceipt/dto/MmisAsetStorageDto.java
 */

// 在 MmisAsetStorageDto 类中的适当位置（建议在其他 @TableField(exist = false) 字段附近）添加以下代码：

/**
 * 物资大类代码（用于信息科入库明细筛选）
 * 支持的值：
 * - 0401: 低值易耗品（信息科库房）
 * - 0402: 耗材（信息科库房）
 * - 0403: 配件（信息科库房）
 */
@TableField(exist = false)
private String itemTypeCode;

// 同时需要添加对应的 getter 和 setter 方法（如果使用 @Data 注解则会自动生成）
public String getItemTypeCode() {
    return itemTypeCode;
}

public void setItemTypeCode(String itemTypeCode) {
    this.itemTypeCode = itemTypeCode;
}

/**
 * 修复步骤：
 * 
 * 1. 打开文件：med-mmis/src/main/java/com/jp/med/mmis/modules/matReceipt/dto/MmisAsetStorageDto.java
 * 
 * 2. 找到其他 @TableField(exist = false) 字段的位置，例如：
 *    - private String itemNum;
 *    - private String invType;
 *    - private String processEmpCode;
 *    等等
 * 
 * 3. 在这些字段附近添加上述 itemTypeCode 字段定义
 * 
 * 4. 保存文件并重新编译后端项目
 * 
 * 5. 重启后端服务
 * 
 * 6. 测试前端信息科入库明细功能
 */

/**
 * 验证方法：
 * 
 * 1. 重启后端服务后，访问前端信息科入库明细页面
 * 2. 尝试进行查询操作
 * 3. 如果不再出现 "There is no getter for property named 'itemTypeCode'" 错误，说明修复成功
 * 4. 验证物资大类筛选功能是否正常工作
 */

/**
 * 相关 SQL 查询位置：
 * 
 * 文件：med-mmis/src/main/resources/mapper/matReceipt/read/MmisAsetStorageReadMapper.xml
 * 方法：queryXinxiStorageDetailList
 * 
 * SQL 中第506行使用了 itemTypeCode 参数：
 * <if test="itemTypeCode != null and itemTypeCode != ''">
 *     AND SUBSTRING(d.item_num, 1, 4) = #{itemTypeCode,jdbcType=VARCHAR}
 * </if>
 * 
 * 这就是为什么需要在 DTO 中添加这个字段的原因。
 */
