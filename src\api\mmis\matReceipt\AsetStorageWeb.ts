import request from '@/utils/request'
import { ContentTypes, RequestType } from '@/types/enums/enums'

/**
 * 新增
 * @param param
 */
export function addMmisAsetStorage(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/save',
    method: RequestType.POST,
    contentType: ContentTypes.FORM_DATA,
    data: param,
  })
}

/**
 * 删除
 * @param param
 */
export function deleteMmisAsetStorage(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/delete',
    method: RequestType.DEL,
    data: param,
  })
}

/**
 * 修改
 * @param param
 */
export function updateMmisAsetStorage(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/update',
    method: RequestType.PUT,
    data: param,
  })
}

/**
 * 查询
 * @param param
 */
export function queryMmisAsetStorage(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/list',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询物资入库
 * @param param
 */
export function waitGoReimReceipt(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/waitGoReimReceipt',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询
 * @param param
 */
export function queryMmisDocNum(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/docmentNum',
    method: RequestType.POST,
    data: param,
  })
}
/**
 * 查询
 * @param param
 */
export function hasStroagedMats(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/hasStroagedMats',
    method: RequestType.POST,
    data: param,
  })
}
/**
 * 查询
 * @param param
 */
export function hasStroagedMatsInApply(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/hasStroagedMatsInApply',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询库存物资信息（使用matUniqueCode作为key）
 * @param param 查询参数
 */
export function hasStroagedMatsInApplyByUniqueCode(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/hasStroagedMatsInApplyByUniqueCode',
    method: RequestType.POST,
    data: param,
  })
}


/**
 * OCR识别
 * @param param
 */
export function ocrIdentify(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/ocrIdentify',
    method: RequestType.POST,
    contentType: ContentTypes.FORM_DATA,
    data: param,
  })
}

/**
 * 生成出库单
 * @param param
 */
export function exportStorageFormTem(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/generateStorageTem',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 修改
 * @param param
 */
export function confirmStorageStatus(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/updateStorageStatus',
    method: RequestType.PUT,
    data: param,
  })
}

/**
 * 修改
 * @param param
 */
export function revokeReceiptApply(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/revokeReceiptApply',
    method: RequestType.PUT,
    data: param,
  })
}

/**
 * 修改
 * @param param
 */
export function removeInvoice(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/removeInvoice',
    method: RequestType.PUT,
    data: param,
  })
}

/**
 * 根据物资编码（特殊编码）或者就是物资编码查询相关的入库记录
 * @param param
 */
export function queryStorageRecordsByMat2Code(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/queryStorageRecordsByMat2Code',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 根据选择的入库单生成报销任务
 * @param param
 */
export function saveMmisMatPurcTask(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/saveMmisMatPurcTask',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 根据选择的入库单生成汇总报销任务
 * @param param
 */
export function saveMmisInMatSumPurcTask(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/saveMmisInMatSumPurcTask',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 文件下载
 * @param param
 */
export function mmisInvosFileDownload(data: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/fileDownload',
    method: RequestType.POST,
    responseType: 'blob',
    data: data,
  })
}

/**
 * 查询待结账的物资入库单
 * @param param
 */
export function queryWaitAccountingReceipt(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/queryWaitAccountingReceipt',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 根据详情id集合获取入库记录 然后再打印入库单文件
 * @param param
 */
export function generateStorageTempByDetailIds(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/generateStorageTempByDetailIds',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询入库明细数据 🔍
 * @param param 查询参数
 * @returns 入库明细列表
 */
export function queryStorageDetailList(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/aLLDetailList',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询本周期待结账的入库记录汇总
 * @param param 查询参数
 */
export function queryCurrentPeriodStorageSummary(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/queryCurrentPeriodStorageSummary',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询指定时间段内待结账的入库记录（用于结账预览）
 * @param param 查询参数，包含startDate和endDate
 */
export function queryPendingStorageByPeriod(param: Object) {
  return request({
    url: 'mmisAsetStorage/queryPendingStorageByPeriod',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询信息科入库明细数据 🏥
 * @param param 查询参数
 * @returns 信息科入库明细列表
 */
export function queryXinxiStorageDetailList(param: Object) {
  return request({
    url: 'mmis/mmisAsetStorage/queryXinxiStorageDetailList',
    method: RequestType.POST,
    data: param,
  })
}
